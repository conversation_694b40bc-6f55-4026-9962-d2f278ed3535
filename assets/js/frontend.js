/**
 * WooCommerce WhatsApp Button - Frontend JavaScript
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Initialize WhatsApp button functionality
        initWhatsAppButton();
        
        // Handle floating button visibility
        handleFloatingButtonVisibility();
        
        // Add click tracking
        addClickTracking();
    });

    /**
     * Initialize WhatsApp button functionality
     */
    function initWhatsAppButton() {
        // Add loading state to buttons
        $('.woo-wa-button').on('click', function() {
            var $button = $(this);
            var originalText = $button.text();
            
            // Add loading state
            $button.addClass('loading').text('Opening WhatsApp...');
            
            // Reset after 2 seconds
            setTimeout(function() {
                $button.removeClass('loading').text(originalText);
            }, 2000);
        });

        // Add hover effects for better UX
        $('.woo-wa-button').hover(
            function() {
                $(this).addClass('hovered');
            },
            function() {
                $(this).removeClass('hovered');
            }
        );
    }

    /**
     * Handle floating button visibility based on scroll
     */
    function handleFloatingButtonVisibility() {
        var $floatingButton = $('.woo-wa-button-floating');
        
        if ($floatingButton.length === 0) {
            return;
        }

        var $addToCartButton = $('.single_add_to_cart_button');
        var showThreshold = 100; // Show after scrolling 100px

        $(window).on('scroll', function() {
            var scrollTop = $(window).scrollTop();
            var addToCartVisible = false;

            // Check if add to cart button is visible
            if ($addToCartButton.length > 0) {
                var addToCartOffset = $addToCartButton.offset().top;
                var windowHeight = $(window).height();
                addToCartVisible = (addToCartOffset < scrollTop + windowHeight) && 
                                 (addToCartOffset + $addToCartButton.height() > scrollTop);
            }

            // Show floating button when scrolled down and add to cart is not visible
            if (scrollTop > showThreshold && !addToCartVisible) {
                $floatingButton.addClass('visible').fadeIn(300);
            } else {
                $floatingButton.removeClass('visible').fadeOut(300);
            }
        });
    }

    /**
     * Add click tracking for analytics
     */
    function addClickTracking() {
        $('.woo-wa-button').on('click', function() {
            var buttonType = $(this).hasClass('woo-wa-button-floating') ? 'floating' : 'inline';
            var productId = $('input[name="add-to-cart"]').val() ||
                           $('button[name="add-to-cart"]').val() ||
                           'unknown';

            // Track with Google Analytics if available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'whatsapp_button_click', {
                    'event_category': 'engagement',
                    'event_label': buttonType,
                    'custom_parameter_1': productId
                });
            }

            // Track with Google Tag Manager if available
            if (typeof dataLayer !== 'undefined') {
                dataLayer.push({
                    'event': 'whatsapp_button_click',
                    'button_type': buttonType,
                    'product_id': productId
                });
            }

            // Console log for debugging (remove in production)
            console.log('WhatsApp button clicked:', {
                type: buttonType,
                productId: productId,
                url: this.href
            });
        });
    }

    /**
     * Add accessibility improvements
     */
    function addAccessibilityImprovements() {
        $('.woo-wa-button').each(function() {
            var $button = $(this);
            
            // Add ARIA labels
            $button.attr('aria-label', 'Contact us via WhatsApp about this product');
            $button.attr('role', 'button');
            
            // Add keyboard support
            $button.on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    window.open(this.href, '_blank', 'noopener');
                }
            });
        });
    }

    // Initialize accessibility improvements
    addAccessibilityImprovements();

    /**
     * Handle responsive behavior
     */
    function handleResponsiveBehavior() {
        var $window = $(window);
        var $buttons = $('.woo-wa-button');

        function adjustButtonSize() {
            var windowWidth = $window.width();
            
            $buttons.each(function() {
                var $button = $(this);
                
                if (windowWidth < 480) {
                    $button.addClass('mobile-size');
                } else {
                    $button.removeClass('mobile-size');
                }
            });
        }

        // Initial adjustment
        adjustButtonSize();

        // Adjust on resize
        $window.on('resize', debounce(adjustButtonSize, 250));
    }

    // Initialize responsive behavior
    handleResponsiveBehavior();

    /**
     * Debounce function for performance
     */
    function debounce(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

})(jQuery);
