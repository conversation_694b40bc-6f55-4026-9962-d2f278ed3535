/**
 * Woo<PERSON>ommerce WhatsApp <PERSON><PERSON> - Frontend Styles
 */

/* Main WhatsApp <PERSON><PERSON> Styles */
.woo-wa-button {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white !important;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    text-decoration: none !important;
    display: inline-block;
    margin: 15px 0;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(37, 211, 102, 0.3);
    text-align: center;
    min-width: 200px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    line-height: 1.4;
}

.woo-wa-button:before {
    content: "📱";
    margin-right: 8px;
    font-size: 18px;
}

.woo-wa-button:hover {
    background: linear-gradient(135deg, #128c7e 0%, #25d366 100%);
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
}

.woo-wa-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
}

/* Floating <PERSON><PERSON> Styles */
.woo-wa-button-floating {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    border-radius: 50px;
    padding: 15px 20px;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    animation: pulse 2s infinite;
    min-width: auto;
}

.woo-wa-button-floating:before {
    margin-right: 5px;
}

/* Pulse Animation */
@keyframes pulse {
    0% { 
        transform: scale(1);
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    }
    50% { 
        transform: scale(1.05);
        box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    }
}

/* Container Styles */
.woo-wa-button-container {
    margin: 15px 0;
    text-align: left;
}

.woo-wa-button-floating-container {
    position: relative;
}

/* Responsive Design */
@media (max-width: 768px) {
    .woo-wa-button {
        width: 100%;
        text-align: center;
        font-size: 15px;
        padding: 14px 20px;
    }
    
    .woo-wa-button-floating {
        bottom: 15px;
        right: 15px;
        padding: 12px 16px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .woo-wa-button {
        font-size: 14px;
        padding: 12px 18px;
    }
    
    .woo-wa-button-floating {
        bottom: 10px;
        right: 10px;
        padding: 10px 14px;
        font-size: 13px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .woo-wa-button {
        box-shadow: 0 2px 10px rgba(37, 211, 102, 0.5);
    }
    
    .woo-wa-button:hover {
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.6);
    }
    
    .woo-wa-button-floating {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.6);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .woo-wa-button {
        background: #25d366;
        border: 2px solid #128c7e;
    }
    
    .woo-wa-button:hover {
        background: #128c7e;
        border-color: #25d366;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .woo-wa-button {
        transition: none;
    }
    
    .woo-wa-button-floating {
        animation: none;
    }
    
    .woo-wa-button:hover {
        transform: none;
    }
}

/* Print Styles */
@media print {
    .woo-wa-button,
    .woo-wa-button-floating {
        display: none;
    }
}
