<?php
/**
 * Plugin Name: WooCommerce WhatsApp Button
 * Plugin URI: https://harunstudio.com
 * Description: Add WhatsApp button to WooCommerce products with customizable message template and styling options.
 * Version: 1.0.0
 * Author: Harun Studio
 * Author URI: https://harunstudio.com
 * Text Domain: woo-wa-button
 * Domain Path: /languages
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WOO_WA_BUTTON_VERSION', '1.0.0');
define('WOO_WA_BUTTON_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WOO_WA_BUTTON_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('WOO_WA_BUTTON_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main WooCommerce WhatsApp Button Class
 */
class WooCommerce_WhatsApp_Button {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Add settings link to plugin page
        add_filter('plugin_action_links_' . WOO_WA_BUTTON_PLUGIN_BASENAME, array($this, 'add_settings_link'));
        
        // Check if WooCommerce is active
        add_action('admin_init', array($this, 'check_woocommerce'));
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Only proceed if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Include required files
        $this->includes();
        
        // Initialize components
        $this->init_hooks();
    }

    /**
     * Include required files
     */
    private function includes() {
        require_once WOO_WA_BUTTON_PLUGIN_PATH . 'includes/class-admin.php';
        require_once WOO_WA_BUTTON_PLUGIN_PATH . 'includes/class-frontend.php';
        require_once WOO_WA_BUTTON_PLUGIN_PATH . 'includes/class-settings.php';
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize admin
        if (is_admin()) {
            new WOO_WA_Button_Admin();
        }
        
        // Initialize frontend
        if (!is_admin()) {
            new WOO_WA_Button_Frontend();
        }
        
        // Initialize settings
        new WOO_WA_Button_Settings();
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain('woo-wa-button', false, dirname(WOO_WA_BUTTON_PLUGIN_BASENAME) . '/languages');
    }

    /**
     * Add settings link to plugin page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('options-general.php?page=woo-wa-button') . '">' . __('Settings', 'woo-wa-button') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Check if WooCommerce is active
     */
    public function check_woocommerce() {
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            deactivate_plugins(WOO_WA_BUTTON_PLUGIN_BASENAME);
        }
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="error"><p><strong>' . __('WooCommerce WhatsApp Button', 'woo-wa-button') . '</strong> ' . __('requires WooCommerce to be installed and active.', 'woo-wa-button') . '</p></div>';
    }
}

// Initialize the plugin
new WooCommerce_WhatsApp_Button();

/**
 * Plugin activation hook
 */
register_activation_hook(__FILE__, 'woo_wa_button_activate');
function woo_wa_button_activate() {
    // Set default options
    $default_options = array(
        'whatsapp_number' => '',
        'message_template' => "Halo, saya tertarik untuk membeli produk ini:\n\n*Nama Produk:* {product_name}\n*Harga:* {product_price}\n*URL Produk:* {product_url}\n\nTerima kasih.",
        'button_position' => 'after_add_to_cart',
        'button_text' => 'Chat via WhatsApp',
        'custom_css' => '',
        'enabled_categories' => array(),
        'show_on_all_categories' => 'yes'
    );
    
    add_option('woo_wa_button_settings', $default_options);
}

/**
 * Plugin deactivation hook
 */
register_deactivation_hook(__FILE__, 'woo_wa_button_deactivate');
function woo_wa_button_deactivate() {
    // Clean up if needed
}
