<?php
/**
 * Settings Class
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WOO_WA_Button_Settings {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'settings_init'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('WooCommerce WhatsApp Button', 'woo-wa-button'),
            __('WhatsApp Button', 'woo-wa-button'),
            'manage_options',
            'woo-wa-button',
            array($this, 'options_page')
        );
    }

    /**
     * Initialize settings
     */
    public function settings_init() {
        register_setting('woo_wa_button', 'woo_wa_button_settings');

        // General Settings Section
        add_settings_section(
            'woo_wa_button_general_section',
            __('General Settings', 'woo-wa-button'),
            array($this, 'general_section_callback'),
            'woo_wa_button'
        );

        // WhatsApp Number Field
        add_settings_field(
            'whatsapp_number',
            __('WhatsApp Number', 'woo-wa-button'),
            array($this, 'whatsapp_number_render'),
            'woo_wa_button',
            'woo_wa_button_general_section'
        );

        // Message Template Field
        add_settings_field(
            'message_template',
            __('Message Template', 'woo-wa-button'),
            array($this, 'message_template_render'),
            'woo_wa_button',
            'woo_wa_button_general_section'
        );

        // Button Position Field
        add_settings_field(
            'button_position',
            __('Button Position', 'woo-wa-button'),
            array($this, 'button_position_render'),
            'woo_wa_button',
            'woo_wa_button_general_section'
        );

        // Button Text Field
        add_settings_field(
            'button_text',
            __('Button Text', 'woo-wa-button'),
            array($this, 'button_text_render'),
            'woo_wa_button',
            'woo_wa_button_general_section'
        );

        // Category Settings Section
        add_settings_section(
            'woo_wa_button_category_section',
            __('Category Settings', 'woo-wa-button'),
            array($this, 'category_section_callback'),
            'woo_wa_button'
        );

        // Show on All Categories Field
        add_settings_field(
            'show_on_all_categories',
            __('Show on All Categories', 'woo-wa-button'),
            array($this, 'show_on_all_categories_render'),
            'woo_wa_button',
            'woo_wa_button_category_section'
        );

        // Enabled Categories Field
        add_settings_field(
            'enabled_categories',
            __('Enabled Categories', 'woo-wa-button'),
            array($this, 'enabled_categories_render'),
            'woo_wa_button',
            'woo_wa_button_category_section'
        );

        // Styling Section
        add_settings_section(
            'woo_wa_button_styling_section',
            __('Styling', 'woo-wa-button'),
            array($this, 'styling_section_callback'),
            'woo_wa_button'
        );

        // Custom CSS Field
        add_settings_field(
            'custom_css',
            __('Custom CSS', 'woo-wa-button'),
            array($this, 'custom_css_render'),
            'woo_wa_button',
            'woo_wa_button_styling_section'
        );
    }

    /**
     * General section callback
     */
    public function general_section_callback() {
        echo __('Configure the basic WhatsApp button settings.', 'woo-wa-button');
    }

    /**
     * Category section callback
     */
    public function category_section_callback() {
        echo __('Choose which product categories should display the WhatsApp button.', 'woo-wa-button');
    }

    /**
     * Styling section callback
     */
    public function styling_section_callback() {
        echo __('Customize the appearance of the WhatsApp button.', 'woo-wa-button');
    }

    /**
     * WhatsApp number field render
     */
    public function whatsapp_number_render() {
        $options = get_option('woo_wa_button_settings');
        ?>
        <input type='text' name='woo_wa_button_settings[whatsapp_number]' value='<?php echo esc_attr($options['whatsapp_number'] ?? ''); ?>' placeholder='628123456789' class='regular-text'>
        <p class="description"><?php _e('Enter WhatsApp number with country code (without + sign). Example: 628123456789', 'woo-wa-button'); ?></p>
        <?php
    }

    /**
     * Message template field render
     */
    public function message_template_render() {
        $options = get_option('woo_wa_button_settings');
        $default_template = "Halo, saya tertarik untuk membeli produk ini:\n\n*Nama Produk:* {product_name}\n*Harga:* {product_price}\n*URL Produk:* {product_url}\n\nTerima kasih.";
        ?>
        <textarea name='woo_wa_button_settings[message_template]' rows='8' cols='50' class='large-text'><?php echo esc_textarea($options['message_template'] ?? $default_template); ?></textarea>
        <p class="description">
            <?php _e('Available placeholders:', 'woo-wa-button'); ?><br>
            <code>{product_name}</code> - <?php _e('Product name', 'woo-wa-button'); ?><br>
            <code>{product_price}</code> - <?php _e('Product price', 'woo-wa-button'); ?><br>
            <code>{product_url}</code> - <?php _e('Product URL', 'woo-wa-button'); ?><br>
            <?php _e('Use *text* for bold formatting in WhatsApp.', 'woo-wa-button'); ?>
        </p>
        <?php
    }

    /**
     * Button position field render
     */
    public function button_position_render() {
        $options = get_option('woo_wa_button_settings');
        $position = $options['button_position'] ?? 'after_add_to_cart';
        ?>
        <select name='woo_wa_button_settings[button_position]'>
            <option value='after_add_to_cart' <?php selected($position, 'after_add_to_cart'); ?>><?php _e('After Add to Cart Button', 'woo-wa-button'); ?></option>
            <option value='before_add_to_cart' <?php selected($position, 'before_add_to_cart'); ?>><?php _e('Before Add to Cart Button', 'woo-wa-button'); ?></option>
            <option value='floating' <?php selected($position, 'floating'); ?>><?php _e('Floating Button', 'woo-wa-button'); ?></option>
        </select>
        <?php
    }

    /**
     * Button text field render
     */
    public function button_text_render() {
        $options = get_option('woo_wa_button_settings');
        ?>
        <input type='text' name='woo_wa_button_settings[button_text]' value='<?php echo esc_attr($options['button_text'] ?? 'Chat via WhatsApp'); ?>' class='regular-text'>
        <?php
    }

    /**
     * Show on all categories field render
     */
    public function show_on_all_categories_render() {
        $options = get_option('woo_wa_button_settings');
        $show_all = $options['show_on_all_categories'] ?? 'yes';
        ?>
        <input type='checkbox' name='woo_wa_button_settings[show_on_all_categories]' value='yes' <?php checked($show_all, 'yes'); ?>>
        <label><?php _e('Show WhatsApp button on all product categories', 'woo-wa-button'); ?></label>
        <?php
    }

    /**
     * Enabled categories field render
     */
    public function enabled_categories_render() {
        $options = get_option('woo_wa_button_settings');
        $enabled_categories = $options['enabled_categories'] ?? array();
        
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
        ));

        if (!empty($categories) && !is_wp_error($categories)) {
            echo '<div id="woo-wa-categories" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">';
            foreach ($categories as $category) {
                $checked = in_array($category->term_id, $enabled_categories) ? 'checked' : '';
                echo '<label style="display: block; margin-bottom: 5px;">';
                echo '<input type="checkbox" name="woo_wa_button_settings[enabled_categories][]" value="' . esc_attr($category->term_id) . '" ' . $checked . '> ';
                echo esc_html($category->name);
                echo '</label>';
            }
            echo '</div>';
            echo '<p class="description">' . __('Select specific categories where the WhatsApp button should appear (only applies when "Show on All Categories" is unchecked).', 'woo-wa-button') . '</p>';
        }
    }

    /**
     * Custom CSS field render
     */
    public function custom_css_render() {
        $options = get_option('woo_wa_button_settings');
        ?>
        <textarea name='woo_wa_button_settings[custom_css]' rows='10' cols='50' class='large-text code'><?php echo esc_textarea($options['custom_css'] ?? ''); ?></textarea>
        <p class="description">
            <?php _e('Add custom CSS to style the WhatsApp button. Example:', 'woo-wa-button'); ?><br>
            <code>.woo-wa-button { background-color: #25d366; color: white; }</code>
        </p>
        <?php
    }

    /**
     * Options page
     */
    public function options_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('WooCommerce WhatsApp Button Settings', 'woo-wa-button'); ?></h1>
            <form action='options.php' method='post'>
                <?php
                settings_fields('woo_wa_button');
                do_settings_sections('woo_wa_button');
                submit_button();
                ?>
            </form>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Toggle category selection based on "show on all categories" checkbox
            $('input[name="woo_wa_button_settings[show_on_all_categories]"]').change(function() {
                if ($(this).is(':checked')) {
                    $('#woo-wa-categories').hide();
                } else {
                    $('#woo-wa-categories').show();
                }
            }).trigger('change');
        });
        </script>
        <?php
    }
}
