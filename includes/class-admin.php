<?php
/**
 * Admin Class
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WOO_WA_Button_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_notices', array($this, 'admin_notices'));
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our settings page
        if ($hook !== 'settings_page_woo-wa-button') {
            return;
        }

        wp_enqueue_script('jquery');
        
        // Add admin styles
        wp_add_inline_style('wp-admin', '
            .woo-wa-button-preview {
                background-color: #25d366;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                text-decoration: none;
                display: inline-block;
                margin: 10px 0;
                font-weight: bold;
            }
            .woo-wa-button-preview:hover {
                background-color: #128c7e;
                color: white;
            }
            .woo-wa-button-preview:before {
                content: "📱 ";
            }
        ');
    }

    /**
     * Show admin notices
     */
    public function admin_notices() {
        $options = get_option('woo_wa_button_settings');
        
        // Check if WhatsApp number is set
        if (empty($options['whatsapp_number'])) {
            $settings_url = admin_url('options-general.php?page=woo-wa-button');
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>' . __('WooCommerce WhatsApp Button', 'woo-wa-button') . '</strong>: ';
            echo sprintf(
                __('Please configure your WhatsApp number in the <a href="%s">settings page</a> to enable the WhatsApp button.', 'woo-wa-button'),
                esc_url($settings_url)
            );
            echo '</p>';
            echo '</div>';
        }
    }
}
