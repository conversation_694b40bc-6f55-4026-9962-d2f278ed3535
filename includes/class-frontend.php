<?php
/**
 * Frontend Class
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WOO_WA_Button_Frontend {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('init', array($this, 'init_hooks'));
    }

    /**
     * Initialize hooks based on button position
     */
    public function init_hooks() {
        $options = get_option('woo_wa_button_settings');
        $position = $options['button_position'] ?? 'after_add_to_cart';

        switch ($position) {
            case 'before_add_to_cart':
                add_action('woocommerce_single_product_summary', array($this, 'display_whatsapp_button'), 25);
                break;
            case 'after_add_to_cart':
                add_action('woocommerce_single_product_summary', array($this, 'display_whatsapp_button'), 35);
                break;
            case 'floating':
                add_action('wp_footer', array($this, 'display_floating_whatsapp_button'));
                break;
        }
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        if (!is_product()) {
            return;
        }

        $options = get_option('woo_wa_button_settings');

        // Enqueue CSS file
        wp_enqueue_style(
            'woo-wa-button-frontend',
            WOO_WA_BUTTON_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            WOO_WA_BUTTON_VERSION
        );

        // Enqueue JavaScript file
        wp_enqueue_script(
            'woo-wa-button-frontend',
            WOO_WA_BUTTON_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            WOO_WA_BUTTON_VERSION,
            true
        );

        // Add custom CSS if provided
        if (!empty($options['custom_css'])) {
            wp_add_inline_style('woo-wa-button-frontend', $options['custom_css']);
        }
    }



    /**
     * Display WhatsApp button on product page
     */
    public function display_whatsapp_button() {
        global $product;

        if (!$this->should_display_button($product)) {
            return;
        }

        $options = get_option('woo_wa_button_settings');
        $whatsapp_number = $options['whatsapp_number'] ?? '';
        $button_text = $options['button_text'] ?? 'Chat via WhatsApp';

        if (empty($whatsapp_number)) {
            return;
        }

        // Hook before display
        do_action('woo_wa_button_before_display', $product);

        $whatsapp_url = $this->generate_whatsapp_url($product, $whatsapp_number);

        // Filter for URL modification
        $whatsapp_url = apply_filters('woo_wa_button_whatsapp_url', $whatsapp_url, $product);

        // Filter for button text
        $button_text = apply_filters('woo_wa_button_text', $button_text, $product);

        echo '<div class="woo-wa-button-container">';
        echo '<a href="' . esc_url($whatsapp_url) . '" class="woo-wa-button" target="_blank" rel="noopener" aria-label="' . esc_attr__('Contact us via WhatsApp about this product', 'woo-wa-button') . '">';
        echo esc_html($button_text);
        echo '</a>';
        echo '</div>';

        // Hook after display
        do_action('woo_wa_button_after_display', $product);
    }

    /**
     * Display floating WhatsApp button
     */
    public function display_floating_whatsapp_button() {
        if (!is_product()) {
            return;
        }

        global $product;

        if (!$this->should_display_button($product)) {
            return;
        }

        $options = get_option('woo_wa_button_settings');
        $whatsapp_number = $options['whatsapp_number'] ?? '';
        $button_text = $options['button_text'] ?? 'Chat via WhatsApp';

        if (empty($whatsapp_number)) {
            return;
        }

        // Hook before display
        do_action('woo_wa_button_before_floating_display', $product);

        $whatsapp_url = $this->generate_whatsapp_url($product, $whatsapp_number);

        // Filter for URL modification
        $whatsapp_url = apply_filters('woo_wa_button_whatsapp_url', $whatsapp_url, $product);

        // Filter for button text
        $button_text = apply_filters('woo_wa_button_floating_text', $button_text, $product);

        echo '<div class="woo-wa-button-floating-container">';
        echo '<a href="' . esc_url($whatsapp_url) . '" class="woo-wa-button woo-wa-button-floating" target="_blank" rel="noopener" aria-label="' . esc_attr__('Contact us via WhatsApp about this product', 'woo-wa-button') . '">';
        echo esc_html($button_text);
        echo '</a>';
        echo '</div>';

        // Hook after display
        do_action('woo_wa_button_after_floating_display', $product);
    }

    /**
     * Check if button should be displayed for this product
     */
    private function should_display_button($product) {
        if (!$product || !is_a($product, 'WC_Product')) {
            return false;
        }

        $options = get_option('woo_wa_button_settings');
        $show_on_all = $options['show_on_all_categories'] ?? 'yes';

        // If show on all categories is enabled, display button
        if ($show_on_all === 'yes') {
            return true;
        }

        // Check if product belongs to enabled categories
        $enabled_categories = $options['enabled_categories'] ?? array();
        if (empty($enabled_categories)) {
            return false;
        }

        $product_categories = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'ids'));
        
        return !empty(array_intersect($product_categories, $enabled_categories));
    }

    /**
     * Generate WhatsApp URL with formatted message
     */
    private function generate_whatsapp_url($product, $whatsapp_number) {
        $options = get_option('woo_wa_button_settings');
        $message_template = $options['message_template'] ?? '';

        // Default template if empty
        if (empty($message_template)) {
            $message_template = "Halo, saya tertarik untuk membeli produk ini:\n\n*Nama Produk:* {product_name}\n*Harga:* {product_price}\n*URL Produk:* {product_url}\n\nTerima kasih.";
        }

        // Get product data
        $product_name = $product->get_name();
        $product_price = wc_price($product->get_price());
        $product_url = get_permalink($product->get_id());

        // Replace placeholders
        $message = str_replace(
            array('{product_name}', '{product_price}', '{product_url}'),
            array($product_name, strip_tags($product_price), $product_url),
            $message_template
        );

        // Clean WhatsApp number (remove any non-numeric characters except +)
        $clean_number = preg_replace('/[^0-9+]/', '', $whatsapp_number);
        
        // Remove + if present at the beginning
        if (substr($clean_number, 0, 1) === '+') {
            $clean_number = substr($clean_number, 1);
        }

        // Generate WhatsApp URL
        $whatsapp_url = 'https://wa.me/' . $clean_number . '?text=' . urlencode($message);

        return $whatsapp_url;
    }
}
