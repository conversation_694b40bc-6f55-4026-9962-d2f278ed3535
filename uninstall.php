<?php
/**
 * Uninstall WooCommerce WhatsApp Button
 *
 * @package WooCommerce_WhatsApp_Button
 */

// If uninstall not called from WordPress, then exit.
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Delete plugin options
delete_option('woo_wa_button_settings');

// Delete any transients
delete_transient('woo_wa_button_cache');

// Clean up any custom database tables if created in future versions
// global $wpdb;
// $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}woo_wa_button_logs");

// Clear any cached data
wp_cache_flush();
