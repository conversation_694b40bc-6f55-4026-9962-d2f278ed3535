# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: WooCommerce WhatsApp Button 1.0.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

msgid "Settings"
msgstr ""

msgid "WooCommerce WhatsApp Button"
msgstr ""

msgid "requires WooCommerce to be installed and active."
msgstr ""

msgid "WhatsApp Button"
msgstr ""

msgid "General Settings"
msgstr ""

msgid "Configure the basic WhatsApp button settings."
msgstr ""

msgid "WhatsApp Number"
msgstr ""

msgid "Enter WhatsApp number with country code (without + sign). Example: 628123456789"
msgstr ""

msgid "Message Template"
msgstr ""

msgid "Available placeholders:"
msgstr ""

msgid "Product name"
msgstr ""

msgid "Product price"
msgstr ""

msgid "Product URL"
msgstr ""

msgid "Use *text* for bold formatting in WhatsApp."
msgstr ""

msgid "Button Position"
msgstr ""

msgid "After Add to Cart Button"
msgstr ""

msgid "Before Add to Cart Button"
msgstr ""

msgid "Floating Button"
msgstr ""

msgid "Button Text"
msgstr ""

msgid "Category Settings"
msgstr ""

msgid "Choose which product categories should display the WhatsApp button."
msgstr ""

msgid "Show on All Categories"
msgstr ""

msgid "Show WhatsApp button on all product categories"
msgstr ""

msgid "Enabled Categories"
msgstr ""

msgid "Select specific categories where the WhatsApp button should appear (only applies when \"Show on All Categories\" is unchecked)."
msgstr ""

msgid "Styling"
msgstr ""

msgid "Customize the appearance of the WhatsApp button."
msgstr ""

msgid "Custom CSS"
msgstr ""

msgid "Add custom CSS to style the WhatsApp button. Example:"
msgstr ""

msgid "WooCommerce WhatsApp Button Settings"
msgstr ""

msgid "Please configure your WhatsApp number in the <a href=\"%s\">settings page</a> to enable the WhatsApp button."
msgstr ""
