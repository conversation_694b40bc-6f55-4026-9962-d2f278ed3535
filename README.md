# WooCommerce WhatsApp Button

Plugin WordPress yang menambahkan tombol WhatsApp ke halaman produk WooCommerce dengan template pesan yang dapat dikustomisasi.

## Fitur

- ✅ Tombol WhatsApp di halaman produk WooCommerce
- ✅ Template pesan yang dapat dikustomisasi dengan placeholder
- ✅ Pilihan posisi tombol (sebelah Add to Cart atau floating button)
- ✅ Filter berdasarkan kategori produk
- ✅ Custom CSS untuk styling
- ✅ Responsive design
- ✅ Accessibility support
- ✅ Settings page di WordPress admin

## Instalasi

1. Upload folder plugin ke `/wp-content/plugins/`
2. Aktifkan plugin melalui menu 'Plugins' di WordPress admin
3. Konfigurasi plugin melalui Settings > WhatsApp Button

## Konfigurasi

### Pengaturan Dasar

1. **Nomor WhatsApp**: Masukkan nomor WhatsApp dengan kode negara (tanpa tanda +)
   - Contoh: `628123456789`

2. **Template Pesan**: Customize pesan yang akan dikirim
   - Placeholder yang tersedia:
     - `{product_name}` - Nama produk
     - `{product_price}` - Harga produk
     - `{product_url}` - URL produk
   - Gunakan `*text*` untuk format bold di WhatsApp

3. **Posisi Tombol**:
   - After Add to Cart Button (default)
   - Before Add to Cart Button
   - Floating Button

4. **Text Tombol**: Customize text yang ditampilkan di tombol

### Pengaturan Kategori

- **Show on All Categories**: Tampilkan di semua kategori produk
- **Enabled Categories**: Pilih kategori spesifik (jika tidak show all)

### Styling

- **Custom CSS**: Tambahkan CSS custom untuk styling tombol

## Template Pesan Default

```
Halo, saya tertarik untuk membeli produk ini:

*Nama Produk:* {product_name}
*Harga:* {product_price}
*URL Produk:* {product_url}

Terima kasih.
```

## CSS Classes

Plugin menyediakan CSS classes berikut untuk customization:

- `.woo-wa-button` - Class utama untuk tombol
- `.woo-wa-button-floating` - Class untuk floating button
- `.woo-wa-button-container` - Container untuk tombol inline

## Contoh Custom CSS

```css
/* Mengubah warna tombol */
.woo-wa-button {
    background-color: #ff6b6b;
    border-radius: 10px;
}

.woo-wa-button:hover {
    background-color: #ff5252;
}

/* Styling untuk floating button */
.woo-wa-button-floating {
    bottom: 30px;
    right: 30px;
    font-size: 14px;
}
```

## Hooks dan Filters

Plugin menyediakan hooks untuk developer:

```php
// Filter untuk mengubah URL WhatsApp
add_filter('woo_wa_button_whatsapp_url', function($url, $product) {
    // Custom logic
    return $url;
}, 10, 2);

// Action sebelum tombol ditampilkan
add_action('woo_wa_button_before_display', function($product) {
    // Custom logic
});
```

## Requirements

- WordPress 5.0+
- WooCommerce 3.0+
- PHP 7.4+

## Changelog

### 1.0.0
- Initial release
- Basic WhatsApp button functionality
- Settings page
- Category filtering
- Custom CSS support
- Responsive design

## Support

Untuk support dan pertanyaan, silakan hubungi [PowerBackdoor](https://powerbackdoor.com).

## License

GPL v2 or later
